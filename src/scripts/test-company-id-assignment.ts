/**
 * Test script to verify company ID assignment is working correctly
 * This script checks:
 * 1. If there are any users without company IDs
 * 2. Assigns company IDs to users who don't have them
 * 3. Verifies the assignment was successful
 * 
 * To run this script:
 * bun src/scripts/test-company-id-assignment.ts
 */

import { db } from "@/lib/prisma";
import { assignCompanyIdsToExistingUsers } from "@/actions/users/company-id";

async function main() {
  console.log("🔍 Checking for users without company IDs...");
  
  try {
    // First, check how many users don't have company IDs
    const usersWithoutCompanyId = await db.user.findMany({
      where: {
        companyId: null,
        role: "OWNER", // Only check owners
      },
      select: {
        id: true,
        email: true,
        username: true,
        provider: true,
        createdAt: true,
      },
    });

    console.log(`📊 Found ${usersWithoutCompanyId.length} users without company IDs:`);
    
    if (usersWithoutCompanyId.length > 0) {
      usersWithoutCompanyId.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.email || user.username} (${user.provider || 'credentials'}) - Created: ${user.createdAt.toLocaleDateString()}`);
      });

      console.log("\n🔧 Assigning company IDs to these users...");
      
      // Assign company IDs to users who don't have them
      const result = await assignCompanyIdsToExistingUsers();
      
      if (result.success) {
        console.log(`✅ Successfully assigned company IDs to ${result.count} users.`);
        
        // Verify the assignment
        console.log("\n🔍 Verifying assignment...");
        const stillWithoutCompanyId = await db.user.findMany({
          where: {
            companyId: null,
            role: "OWNER",
          },
          select: {
            id: true,
            email: true,
            username: true,
          },
        });
        
        if (stillWithoutCompanyId.length === 0) {
          console.log("✅ All users now have company IDs!");
        } else {
          console.log(`⚠️  ${stillWithoutCompanyId.length} users still don't have company IDs:`);
          stillWithoutCompanyId.forEach((user, index) => {
            console.log(`  ${index + 1}. ${user.email || user.username}`);
          });
        }
      } else {
        console.error(`❌ Failed to assign company IDs: ${result.error}`);
      }
    } else {
      console.log("✅ All users already have company IDs!");
    }

    // Show some statistics
    console.log("\n📈 Company ID Statistics:");
    const totalOwners = await db.user.count({
      where: { role: "OWNER" },
    });
    
    const ownersWithCompanyId = await db.user.count({
      where: { 
        role: "OWNER",
        companyId: { not: null },
      },
    });

    console.log(`  Total OWNER users: ${totalOwners}`);
    console.log(`  Users with company IDs: ${ownersWithCompanyId}`);
    console.log(`  Users without company IDs: ${totalOwners - ownersWithCompanyId}`);

    // Show latest company IDs
    console.log("\n🆔 Latest Company IDs:");
    const latestUsers = await db.user.findMany({
      where: {
        companyId: { not: null },
      },
      orderBy: {
        companyId: "desc",
      },
      take: 5,
      select: {
        companyId: true,
        email: true,
        username: true,
        provider: true,
        createdAt: true,
      },
    });

    latestUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.companyId} - ${user.email || user.username} (${user.provider || 'credentials'})`);
    });

  } catch (error) {
    console.error("❌ Error running test:", error);
  } finally {
    await db.$disconnect();
  }
}

main()
  .then(() => {
    console.log("\n🎉 Test completed successfully.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Test failed:", error);
    process.exit(1);
  });
