/**
 * Simple verification script to check if the company ID fix is working
 */

import { db } from "@/lib/prisma";

async function main() {
  console.log("🔍 Verifying company ID assignment fix...");
  
  try {
    // Check all users and their company ID status
    const users = await db.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        provider: true,
        role: true,
        companyId: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(`\n📊 Found ${users.length} total users:`);
    
    let ownersWithCompanyId = 0;
    let ownersWithoutCompanyId = 0;
    
    users.forEach((user, index) => {
      const provider = user.provider || 'credentials';
      const companyId = user.companyId || 'NO_COMPANY_ID';
      const identifier = user.email || user.username || 'no-identifier';
      
      console.log(`  ${index + 1}. ${identifier} (${provider}) - ${user.role} - ${companyId}`);
      
      if (user.role === 'OWNER') {
        if (user.companyId) {
          ownersWithCompanyId++;
        } else {
          ownersWithoutCompanyId++;
        }
      }
    });

    console.log(`\n📈 Summary:`);
    console.log(`  OWNER users with company ID: ${ownersWithCompanyId}`);
    console.log(`  OWNER users without company ID: ${ownersWithoutCompanyId}`);
    
    if (ownersWithoutCompanyId === 0) {
      console.log(`\n✅ SUCCESS: All OWNER users have company IDs!`);
    } else {
      console.log(`\n⚠️  WARNING: ${ownersWithoutCompanyId} OWNER users still need company IDs`);
    }

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await db.$disconnect();
  }
}

main();
