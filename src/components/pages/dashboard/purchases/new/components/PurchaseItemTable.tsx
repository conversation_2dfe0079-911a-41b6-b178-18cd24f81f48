"use client";

import React from "react";
import { PurchaseFormValues, Product } from "../types";
import { Control, UseFieldArrayRemove, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@heroicons/react/24/outline";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { InfoIcon } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import CustomDropdownProductSelect from "./CustomDropdownProductSelect";

interface PurchaseItemTableProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  products: Product[];
  items: PurchaseFormValues["items"];
  fields: any[];
  append: (value: any) => void;
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
}

const PurchaseItemTable: React.FC<PurchaseItemTableProps> = ({
  control,
  isPending,
  products,
  items,
  fields,
  append,
  remove,
  handleProductChange,
}) => {
  const form = useFormContext<PurchaseFormValues>();
  return (
    <div className="space-y-4 w-full">
      {/* Price Includes Tax Checkbox */}
      <div className="flex items-center space-x-2 mb-4">
        <FormField
          control={control}
          name="priceIncludesTax"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={isPending}
                  className="cursor-pointer"
                />
              </FormControl>
              <div className="flex items-center space-x-1">
                <FormLabel className="cursor-pointer">
                  Harga Termasuk Pajak
                </FormLabel>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        Jika dicentang, pajak akan dihitung dari harga yang
                        sudah termasuk pajak.
                      </p>
                      <p>
                        Jika tidak dicentang, pajak akan ditambahkan ke harga
                        dasar.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </FormItem>
          )}
        />
      </div>

      <div
        className="overflow-x-auto"
        style={{ maxWidth: "100%", overflowX: "auto", minHeight: "170px" }}
      >
        <table
          className="w-full border-collapse table-auto"
          style={{ minWidth: "820px" }}
        >
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 h-12">
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "300px" }}
              >
                Produk <span className="text-red-500 font-bold">*</span>
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "100px" }}
              >
                Kuantitas
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "100px" }}
              >
                Unit
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "150px" }}
              >
                Harga satuan
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "100px" }}
              >
                Pajak
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "120px" }}
              >
                Jumlah
              </th>
              <th
                className="text-right py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "70px" }}
              ></th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => (
              <CustomDropdownProductSelect
                key={field.id}
                control={control}
                index={index}
                field={field}
                products={products}
                items={items}
                remove={remove}
                handleProductChange={handleProductChange}
                isPending={isPending}
                canRemove={fields.length > 1}
              />
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() =>
            append({
              productId: "",
              quantity: 1,
              costAtPurchase: 0,
              discountPercentage: 0,
              discountAmount: 0,
              unit: "Buah",
              tax: "",
            })
          }
          disabled={isPending}
          className="cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Tambah Item
        </Button>
      </div>
    </div>
  );
};

export default PurchaseItemTable;
