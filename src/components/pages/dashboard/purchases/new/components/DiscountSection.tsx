"use client";

import React, { useState } from "react";
import { Control, useFormContext } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PurchaseFormValues } from "../types";
import { formatCurrency } from "@/lib/utils";

interface DiscountSectionProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  items: PurchaseFormValues["items"];
}

const DiscountSection: React.FC<DiscountSectionProps> = ({
  control,
  isPending,
  items,
}) => {
  const form = useFormContext<PurchaseFormValues>();
  const [discountType, setDiscountType] = useState<"percentage" | "amount">(
    "percentage"
  );

  // Calculate subtotal before discount
  const subtotalBeforeDiscount = items.reduce((sum, item) => {
    const quantity = item?.quantity ?? 0;
    const cost = item?.costAtPurchase ?? 0;
    return sum + quantity * cost;
  }, 0);

  // Get current discount values
  const globalDiscountPercentage = form.watch("globalDiscountPercentage") || 0;
  const globalDiscountAmount = form.watch("globalDiscountAmount") || 0;

  // Calculate discount amount
  const discountAmount = globalDiscountPercentage > 0 
    ? subtotalBeforeDiscount * (globalDiscountPercentage / 100)
    : globalDiscountAmount;

  const handleDiscountChange = (
    value: string,
    type: "percentage" | "amount"
  ) => {
    const numericValue = parseFloat(value) || 0;

    if (type === "percentage") {
      form.setValue("globalDiscountPercentage", numericValue);
      form.setValue("globalDiscountAmount", 0);
    } else {
      form.setValue("globalDiscountAmount", numericValue);
      form.setValue("globalDiscountPercentage", 0);
    }

    // Recalculate total
    recalculateTotal();
  };

  const recalculateTotal = () => {
    const currentItems = form.getValues("items");
    const priceIncludesTax = form.watch("priceIncludesTax");
    const globalDiscountPercentage = form.watch("globalDiscountPercentage") || 0;
    const globalDiscountAmount = form.watch("globalDiscountAmount") || 0;

    // Calculate subtotal before discount
    const subtotalBeforeDiscount = currentItems.reduce((sum: number, item: any) => {
      const quantity = item?.quantity ?? 0;
      const cost = item?.costAtPurchase ?? 0;
      return sum + quantity * cost;
    }, 0);

    // Calculate global discount
    const globalDiscount = globalDiscountPercentage > 0 
      ? subtotalBeforeDiscount * (globalDiscountPercentage / 100)
      : globalDiscountAmount;

    // Calculate subtotal after discount
    const subtotalAfterDiscount = Math.max(0, subtotalBeforeDiscount - globalDiscount);

    // Calculate tax on the discounted amount
    const taxAmount = currentItems.reduce((sum: number, item: any) => {
      const quantity = item?.quantity ?? 0;
      const cost = item?.costAtPurchase ?? 0;
      const taxRate = parseFloat(item?.tax || "0") / 100;
      
      // Calculate item's proportion of the total before discount
      const itemProportion = subtotalBeforeDiscount > 0 
        ? (quantity * cost) / subtotalBeforeDiscount 
        : 0;
      
      // Apply discount proportionally to this item
      const itemAfterDiscount = (quantity * cost) - (globalDiscount * itemProportion);
      
      // Calculate tax on the discounted amount
      if (priceIncludesTax) {
        // If price includes tax, tax is already included
        return sum;
      } else {
        // If price doesn't include tax, add tax to the discounted amount
        return sum + (itemAfterDiscount * taxRate);
      }
    }, 0);

    const total = priceIncludesTax ? subtotalAfterDiscount : subtotalAfterDiscount + taxAmount;
    form.setValue("totalAmount", total);
  };

  // Determine current discount type and value
  const currentDiscountType = globalDiscountPercentage > 0 ? "percentage" : "amount";
  const currentValue = globalDiscountPercentage > 0 ? globalDiscountPercentage : globalDiscountAmount;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <FormLabel className="text-sm font-medium">Diskon Global</FormLabel>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {discountAmount > 0 && (
            <span>Diskon: Rp {discountAmount.toLocaleString("id-ID")}</span>
          )}
        </div>
      </div>
      
      <FormField
        control={control}
        name={
          discountType === "percentage"
            ? "globalDiscountPercentage"
            : "globalDiscountAmount"
        }
        render={({ field: formField }) => (
          <FormItem className="space-y-0">
            <div className="relative">
              <FormControl>
                <div className="relative">
                  {/* Currency prefix for amount type */}
                  {discountType === "amount" && (
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400 text-sm">
                        Rp
                      </span>
                    </div>
                  )}
                  <Input
                    type="text"
                    placeholder="0"
                    value={currentValue.toString()}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      handleDiscountChange(value, discountType);
                    }}
                    disabled={isPending}
                    className={`w-full pr-16 ${
                      discountType === "amount" ? "pl-8" : ""
                    }`}
                  />
                  {/* Integrated type selector */}
                  <div className="absolute inset-y-0 right-0 flex items-center">
                    <Select
                      value={discountType}
                      onValueChange={(value: "percentage" | "amount") => {
                        setDiscountType(value);
                        // Reset both discount values when switching type
                        form.setValue("globalDiscountPercentage", 0);
                        form.setValue("globalDiscountAmount", 0);
                        recalculateTotal();
                      }}
                      disabled={isPending}
                    >
                      <SelectTrigger className="h-full w-14 border-0 border-l border-gray-200 dark:border-gray-700 rounded-l-none bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">%</SelectItem>
                        <SelectItem value="amount">Rp</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </FormControl>
              <div className="absolute top-full left-0 w-full mt-1">
                <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
              </div>
            </div>
          </FormItem>
        )}
      />
    </div>
  );
};

export default DiscountSection;
