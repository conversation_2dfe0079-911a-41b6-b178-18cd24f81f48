// this file for edge browser compatible
import Google from "next-auth/providers/google";
import type { NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { db as database } from "./prisma";
import bcrypt from "bcryptjs";
import { LoginSchema, EmployeeLoginSchema } from "../schemas/zod";
import { Role } from "@prisma/client";

// Notice this is only an object, not a full Auth.js instance
export default {
  providers: [
    // Provider untuk login dengan email dan password
    Credentials({
      credentials: {
        email: { type: "email" },
        password: { type: "password" },
      },
      async authorize(credentials) {
        console.log("🔐 CREDENTIALS AUTHORIZE CALLED");
        console.log("📧 Credentials received:", {
          email: credentials?.email,
          passwordLength:
            typeof credentials?.password === "string"
              ? credentials.password.length
              : 0,
        });

        // Validasi input menggunakan schema Zod
        const validatedFields = LoginSchema.safeParse(credentials);
        if (!validatedFields.success) {
          console.log(
            "❌ Credentials validation failed:",
            validatedFields.error
          );
          return null;
        }

        // Ambil email dan password dari data yang sudah divalidasi
        const { email, password } = validatedFields.data;
        console.log("✅ Credentials validation passed for email:", email);

        // Cari pengguna berdasarkan email
        console.log("🔍 Looking up user in database:", email);
        const user = await database.user.findUnique({
          where: { email },
        });

        console.log(
          "👤 User found:",
          user
            ? {
                id: user.id,
                email: user.email,
                emailVerified: user.emailVerified,
                hasPassword: !!user.password,
                role: user.role,
              }
            : "No user found"
        );

        // Jika pengguna tidak ditemukan, return null instead of throwing
        if (!user || !user.password) {
          console.log("❌ User not found or no password");
          return null;
        }

        // Temporarily bypass email verification for development
        if (user && !user.emailVerified) {
          console.log(
            "⚠️ Email not verified, but allowing login for development"
          );
          // In production, uncomment the lines below:
          // return null;
        }

        // Bandingkan password yang dimasukkan dengan hash password di database
        console.log("🔑 Comparing passwords");
        const isPasswordValid = await bcrypt.compare(password, user.password);
        console.log("🔑 Password valid:", isPasswordValid);

        if (!isPasswordValid) {
          console.log("❌ Password invalid");
          return null;
        }

        console.log("✅ Authorization successful, returning user");
        return {
          ...user,
          role: user.role ?? Role.CASHIER,
        };
      },
    }),

    // Provider untuk login sebagai karyawan
    Credentials({
      id: "employee-credentials",
      name: "Employee Login",
      credentials: {
        companyUsername: { type: "text", label: "Username Perusahaan" },
        employeeId: { type: "text", label: "ID Karyawan" },
        password: { type: "password", label: "Password" },
      },
      async authorize(credentials) {
        console.log("🔐 AUTH CONFIG: Employee authorization started");

        // Validasi input menggunakan schema Zod
        const validatedFields = EmployeeLoginSchema.safeParse(credentials);
        if (!validatedFields.success) {
          console.log(
            "❌ AUTH CONFIG: Validation failed:",
            validatedFields.error
          );
          return null;
        }

        // Ambil data dari input yang sudah divalidasi
        const { companyUsername, employeeId, password } = validatedFields.data;
        console.log("✅ AUTH CONFIG: Validation passed");

        // Cari owner berdasarkan companyUsername
        console.log(
          "🔍 AUTH CONFIG: Looking for owner with companyUsername:",
          companyUsername
        );
        const owner = await database.user.findUnique({
          where: { companyUsername: companyUsername },
        });

        console.log(
          "👤 AUTH CONFIG: Owner lookup result:",
          owner
            ? {
                id: owner.id,
                username: owner.username,
                companyUsername: owner.companyUsername,
                role: owner.role,
              }
            : "Owner not found"
        );

        // Jika owner tidak ditemukan, lempar error
        if (!owner || owner.role !== Role.OWNER) {
          console.log("❌ AUTH CONFIG: Owner not found or not OWNER role");
          throw new Error("Perusahaan tidak ditemukan!");
        }

        // Cari karyawan berdasarkan employeeId dan ownerId
        console.log("🔍 AUTH CONFIG: Looking for employee with:", {
          employeeId,
          ownerId: owner.id,
        });
        const employee = await database.employee.findFirst({
          where: {
            employeeId,
            ownerId: owner.id,
          },
        });

        console.log(
          "👤 AUTH CONFIG: Employee lookup result:",
          employee
            ? {
                id: employee.id,
                name: employee.name,
                employeeId: employee.employeeId,
                role: employee.role,
                ownerId: employee.ownerId,
              }
            : "Employee not found"
        );

        // Jika karyawan tidak ditemukan, lempar error
        if (!employee) {
          console.log("❌ AUTH CONFIG: Employee not found");
          throw new Error("Karyawan tidak ditemukan!");
        }

        // Bandingkan password yang dimasukkan dengan hash password di database
        console.log("🔑 AUTH CONFIG: Verifying employee password...");
        const isPasswordValid = await bcrypt.compare(
          password,
          employee.password
        );
        console.log(
          "🔑 AUTH CONFIG: Password verification result:",
          isPasswordValid
        );

        if (!isPasswordValid) {
          console.log("❌ AUTH CONFIG: Password verification failed");
          throw new Error("Password salah!");
        }

        console.log("✅ AUTH CONFIG: Employee authentication successful");

        // Return data karyawan dengan format yang sesuai dengan User
        return {
          id: employee.id,
          name: employee.name,
          role: employee.role,
          isEmployee: true,
          ownerId: employee.ownerId,
          employeeId: employee.employeeId,
        };
      },
    }),

    // Provider untuk login dengan Google
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
  ],
} satisfies NextAuthConfig;
